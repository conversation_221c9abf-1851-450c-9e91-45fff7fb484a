# Security Policy

## Reporting Security Vulnerabilities

We take the security of QCode seriously. If you discover a security vulnerability, including zero-day exploits, we appreciate your help in disclosing it to us in a responsible manner.

### How to Report

**Please do NOT report security vulnerabilities through public GitHub issues.**

Instead, please report security vulnerabilities by:

1. **GitHub Security Advisories** (Recommended): Use GitHub's private vulnerability reporting feature by clicking "Report a vulnerability" in the Security tab of this repository
2. **Email**: If the repository owner has made their email address public on their GitHub profile, you may reach out via email
3. **GitHub Issues**: As a last resort, you may create a private draft security advisory to initiate contact

### What to Include

When reporting a security vulnerability, please include:

- **Type of vulnerability** (e.g., XSS, SQL injection, authentication bypass, etc.)
- **Full paths of source file(s) related to the manifestation of the vulnerability**
- **Location of the affected source code** (tag/branch/commit or direct URL)
- **Step-by-step instructions to reproduce the vulnerability**
- **Proof-of-concept or exploit code** (if possible)
- **Impact assessment** - what an attacker might be able to achieve
- **Any special configuration required to reproduce the issue**

### Scope

This security policy applies to:

- The QCode web application
- All source code in this repository
- Dependencies and third-party components used by the application
- The Progressive Web App (PWA) functionality
- Local storage and data handling mechanisms

### Response Timeline

- **Initial Response**: We aim to respond to security reports within 48 hours
- **Status Update**: We will provide regular updates on the progress of addressing the vulnerability
- **Resolution**: We strive to resolve critical vulnerabilities within 7 days and other vulnerabilities within 30 days

### Responsible Disclosure

We follow the principle of coordinated disclosure:

1. Report the vulnerability privately to us first
2. Give us reasonable time to investigate and develop a fix
3. We will work with you to determine an appropriate disclosure timeline
4. Public disclosure will happen after a fix is available and deployed

### Recognition

We believe in giving credit where credit is due:

- We will acknowledge security researchers in our release notes (unless you prefer to remain anonymous)
- For significant vulnerabilities, we may provide a more prominent acknowledgment
- We appreciate responsible disclosure and will treat all reporters with respect

### Security Best Practices

As a PWA that handles user data locally, we implement:

- **Client-side data validation and sanitization**
- **Secure data storage practices**
- **Content Security Policy (CSP) headers**
- **Regular dependency updates and security scanning**
- **Input validation and output encoding**

### Legal

This security policy is intended to give security researchers clear guidelines for conducting vulnerability discovery activities and to protect both researchers and our organization. We will not pursue legal action against researchers who:

- Make a good faith effort to avoid privacy violations, destruction of data, and interruption or degradation of our services
- Only interact with accounts you own or with explicit permission of the account holder
- Do not access or modify user data belonging to others
- Contact us at the first opportunity if you encounter any user data during testing

### Questions

If you have questions about this security policy, please contact us through the same channels used for reporting vulnerabilities.

---

*This security policy is effective as of the date of the last commit to this file and may be updated from time to time.*