<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QCode - Offline</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }
        
        .container {
            max-width: 400px;
            padding: 40px 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }
        
        .icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: white;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
        }
        
        h1 {
            margin: 0 0 10px;
            font-size: 24px;
            font-weight: 600;
        }
        
        p {
            margin: 0 0 30px;
            opacity: 0.9;
            line-height: 1.5;
        }
        
        button {
            background: white;
            color: #667eea;
            border: none;
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        button:hover {
            transform: translateY(-2px);
        }
        
        .features {
            margin-top: 30px;
            text-align: left;
        }
        
        .feature {
            display: flex;
            align-items: center;
            margin: 10px 0;
            opacity: 0.9;
        }
        
        .feature::before {
            content: "✓";
            margin-right: 10px;
            color: #4ade80;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">📱</div>
        <h1>Je bent offline</h1>
        <p>Geen internetverbinding gevonden. Maar geen zorgen - QCode werkt ook offline!</p>
        
        <div class="features">
            <div class="feature">Bekijk je opgeslagen kortingscodes</div>
            <div class="feature">Zoek en filter je codes</div>
            <div class="feature">Voeg nieuwe codes toe</div>
            <div class="feature">Alles wordt gesynchroniseerd zodra je weer online bent</div>
        </div>
        
        <button onclick="window.location.reload()">
            Opnieuw proberen
        </button>
    </div>
    
    <script>
        // Check if online and reload
        window.addEventListener('online', () => {
            window.location.reload()
        })
        
        // Auto-retry when back online
        setInterval(() => {
            if (navigator.onLine) {
                window.location.href = '/'
            }
        }, 5000)
    </script>
</body>
</html>
