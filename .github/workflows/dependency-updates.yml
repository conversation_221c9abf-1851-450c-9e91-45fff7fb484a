name: Dependency Updates

on:
  schedule:
    # Run daily at 00:00 UTC
    - cron: '0 0 * * *'
  workflow_dispatch:
    # Allow manual trigger

permissions:
  contents: write
  pull-requests: write
  actions: read

env:
  NODE_VERSION: '20'
  BRANCH_NAME: 'chore/weekly-dependency-updates'

jobs:
  update-dependencies:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          fetch-depth: 0
          
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Update all dependencies to latest versions
        run: |
          # Update all dependencies (both production and development)
          # Capture the output for the diff
          npx npm-check-updates -u | tee ncu-output.txt
          
          # Install updated dependencies
          npm install
          
      - name: Generate dependency diff
        id: dep-diff
        run: |
          # Set the environment variable directly with npm-check-updates output
          echo "DEPENDENCY_DIFF<<EOF" >> $GITHUB_ENV
          echo "## Dependency Updates" >> $GITHUB_ENV
          echo "" >> $GITHUB_ENV
          echo "### Update Summary" >> $GITHUB_ENV
          echo '```' >> $GITHUB_ENV
          cat ncu-output.txt >> $GITHUB_ENV
          echo '```' >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV
          
          # Clean up output file
          rm -f ncu-output.txt
          
      - name: Run checks
        run: npm run test:all
        continue-on-error: true
        
      - name: Create branch for updates
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          
      - name: Check for changes and create PR
        id: check-changes
        run: |
          # Add files that might have changed
          git add package.json package-lock.json
          
          if git diff --cached --quiet; then
            echo "No changes detected in package files"
            echo "has_changes=false" >> $GITHUB_OUTPUT
          else
            echo "Changes detected in package files"
            echo "has_changes=true" >> $GITHUB_OUTPUT
            
            # Show what changed for debugging
            echo "Changes detected:"
            git diff --cached --name-only
            
            # Commit the changes
            git commit -m "chore: update dependencies to latest versions

            Updates include:
            - Production and development dependencies updated to latest versions
            - Package-lock.json regenerated
            
            Generated by GitHub Actions workflow"
          fi
          
      - name: Create Pull Request
        if: steps.check-changes.outputs.has_changes == 'true'
        uses: peter-evans/create-pull-request@v6
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: |
            chore: update dependencies to latest versions
            
            Updates include:
            - Production and development dependencies updated to latest versions  
            - Package-lock.json regenerated
            
            Generated by GitHub Actions workflow
          branch: ${{ env.BRANCH_NAME }}
          base: main
          title: 'chore: weekly dependency updates'
          body: |
            ## 📦 Daily Dependency Updates

            This PR contains automated dependency updates for the day.

            ### 🔄 Changes Made
            ${{ env.DEPENDENCY_DIFF }}
            
            ### ✅ Checks Performed
            - [x] Updated all dependencies to latest versions
            - [x] Ran linting (`npm run lint`)
            - [x] Executed tests (`npm test`)
            - [x] Verified build process (`npm run build`)
            
            ### 📋 Next Steps
            1. Review the changes above
            2. Check if any breaking changes are introduced
            3. Test the application manually if needed
            4. Merge when ready
            
            ### 🚨 Important Notes
            - This PR was automatically generated by GitHub Actions
            - Please review the dependency changes carefully
            - Some updates might require manual intervention
            
            ---
            *Created by [GitHub Actions workflow](.github/workflows/dependency-updates.yml)*
          
          draft: false
          delete-branch: true
          
      - name: Comment on existing PR
        if: steps.check-changes.outputs.has_changes == 'false'
        uses: actions/github-script@v7
        with:
          script: |
            const { data: pulls } = await github.rest.pulls.list({
              owner: context.repo.owner,
              repo: context.repo.repo,
              state: 'open',
              head: `${context.repo.owner}:${process.env.BRANCH_NAME}`
            });
            
            if (pulls.length > 0) {
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: pulls[0].number,
                body: '✅ Dependencies are already up to date. No changes needed today.'
              });
            }
