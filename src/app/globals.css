@import "tailwindcss";

:root {
  /* Base colors - Enhanced with warmer tones */
  --background: #fafbfc;
  --background-light: #f1f5f9;
  --foreground: #0f172a;
  
  /* Card system - Enhanced with better contrast */
  --card-bg: #ffffff;
  --card-border: #e2e8f0;
  --card-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.12), 0 1px 2px -1px rgb(0 0 0 / 0.12);
  --card-shadow-hover: 0 8px 25px -5px rgb(0 0 0 / 0.1), 0 4px 6px -2px rgb(0 0 0 / 0.05);
  
  /* Input system */
  --input-bg: #ffffff;
  --input-border: #cbd5e1;
  --input-focus: #3b82f6;
  
  /* Text system - Enhanced contrast */
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-muted: #64748b;
  
  /* Enhanced accent colors with more vibrancy */
  --accent-blue: #2563eb;
  --accent-blue-hover: #1d4ed8;
  --accent-blue-50: #eff6ff;
  --accent-blue-100: #dbeafe;
  --accent-blue-200: #bfdbfe;
  --accent-blue-300: #93c5fd;
  --accent-blue-400: #60a5fa;
  --accent-blue-500: #3b82f6;
  --accent-blue-600: #2563eb;
  --accent-blue-700: #1d4ed8;
  --accent-blue-800: #1e40af;
  --accent-blue-900: #1e3a8a;
  --accent-green: #059669;
  --accent-red: #dc2626;
  --accent-orange: #ea580c;
  --accent-yellow: #d97706;
  --accent-purple: #7c3aed;
  --accent-indigo: #4f46e5;
  
  /* Enhanced status colors */
  --success-bg: #ecfdf5;
  --success-border: #a7f3d0;
  --warning-bg: #fffbeb;
  --warning-border: #fed7aa;
  --error-bg: #fef2f2;
  --error-border: #fca5a5;
  
  /* Component-specific colors - Enhanced */
  --filter-bg: #f8fafc;
  --filter-border: #e2e8f0;
  --code-display-bg: #f8fafc;
  --code-display-bg-light: #f1f5f9;
  --code-display-border: #cbd5e1;
  --stat-card-bg: #f8fafc;
  --stat-card-bg-light: #f1f5f9;
  --stat-card-border: #e2e8f0;
  --menu-bg: #ffffff;
  --menu-border: #e2e8f0;
  --menu-hover: #f8fafc;
  
  /* Settings modal specific colors */
  --settings-sidebar-bg: #f8fafc;
  --settings-sidebar-border: #e2e8f0;
  --settings-active-bg: #dbeafe;
  --settings-active-text: #1e40af;
  --settings-active-border: #3b82f6;
}

.dark {
  /* Base colors - Enhanced dark theme */
  --background: #0f172a;
  --background-light: #020617;
  --foreground: #f8fafc;
  
  /* Card system - Enhanced with better contrast */
  --card-bg: #1e293b;
  --card-border: #334155;
  --card-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.4), 0 1px 2px -1px rgb(0 0 0 / 0.4);
  --card-shadow-hover: 0 8px 25px -5px rgb(0 0 0 / 0.25), 0 4px 6px -2px rgb(0 0 0 / 0.1);
  
  /* Input system */
  --input-bg: #334155;
  --input-border: #475569;
  --input-focus: #60a5fa;
  
  /* Text system - Enhanced contrast */
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  
  /* Enhanced accent colors for dark mode */
  --accent-blue: #60a5fa;
  --accent-blue-hover: #93c5fd;
  --accent-blue-50: #1e3a8a;
  --accent-blue-100: #1e40af;
  --accent-blue-200: #1d4ed8;
  --accent-blue-300: #2563eb;
  --accent-blue-400: #3b82f6;
  --accent-blue-500: #60a5fa;
  --accent-blue-600: #93c5fd;
  --accent-blue-700: #bfdbfe;
  --accent-blue-800: #dbeafe;
  --accent-blue-900: #eff6ff;
  --accent-green: #22d3ee;
  --accent-red: #f87171;
  --accent-orange: #fb923c;
  --accent-yellow: #fbbf24;
  --accent-purple: #a78bfa;
  --accent-indigo: #818cf8;
  
  /* Enhanced status colors */
  --success-bg: #064e3b;
  --success-border: #059669;
  --warning-bg: #451a03;
  --warning-border: #d97706;
  --error-bg: #450a0a;
  --error-border: #dc2626;
  
  /* Component-specific colors - Enhanced */
  --filter-bg: #334155;
  --filter-border: #475569;
  --code-display-bg: #334155;
  --code-display-bg-light: #475569;
  --code-display-border: #475569;
  --stat-card-bg: #334155;
  --stat-card-bg-light: #475569;
  --stat-card-border: #475569;
  --menu-bg: #1e293b;
  --menu-border: #334155;
  --menu-hover: #334155;
  
  /* Settings modal specific colors for dark mode */
  --settings-sidebar-bg: #1e293b;
  --settings-sidebar-border: #334155;
  --settings-active-bg: #1e40af;
  --settings-active-text: #dbeafe;
  --settings-active-border: #60a5fa;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

html {
  transition: background 0.3s ease;
}

body {
  background: linear-gradient(135deg, var(--background) 0%, var(--background-light) 100%);
  color: var(--foreground);
  font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
  transition: background 0.3s ease, color 0.3s ease;
  min-height: 100vh;
}

/* FIXED: Use consistent gradient approach for both themes */
.dark body {
  background: linear-gradient(135deg, var(--background) 0%, var(--background-light) 100%);
}

/* Custom scrollbar for light mode */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar-track {
  background: #1e293b;
}

.dark ::-webkit-scrollbar-thumb {
  background: #475569;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Focus styles */
*:focus-visible {
  outline: 2px solid var(--accent-blue);
  outline-offset: 2px;
}

/* Smooth transitions for all interactive elements */
button, input, select, textarea {
  transition: all 0.2s ease-in-out;
}

/* Card hover effects */
.card-hover {
  transition: all 0.2s ease-in-out;
}

.card-hover:hover {
  transform: translateY(-1px);
  box-shadow: var(--card-shadow-hover);
}

/* Theme-aware utility classes */
.theme-card {
  background-color: var(--card-bg);
  border-color: var(--card-border);
  color: var(--text-primary);
}

.theme-text-primary {
  color: var(--text-primary);
}

.theme-text-secondary {
  color: var(--text-secondary);
}

.theme-text-muted {
  color: var(--text-muted);
}

.theme-input {
  background-color: var(--input-bg);
  border-color: var(--input-border);
  color: var(--text-primary);
}

.theme-input:focus {
  border-color: var(--input-focus);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.theme-filter {
  background-color: var(--filter-bg);
  border-color: var(--filter-border);
}

.theme-code-display {
  background: linear-gradient(to right, var(--code-display-bg), var(--code-display-bg-light));
  border-color: var(--code-display-border);
}

.theme-stat-card {
  background: linear-gradient(to bottom right, var(--stat-card-bg), var(--stat-card-bg-light));
  border-color: var(--stat-card-border);
}

.theme-menu {
  background-color: var(--menu-bg);
  border-color: var(--menu-border);
}

.theme-menu-hover:hover {
  background-color: var(--menu-hover);
}

/* Theme-aware stat gradients */
.stat-gradient-green {
  background: linear-gradient(135deg, var(--accent-green) 0%, #059669 100%);
}

.stat-gradient-red {
  background: linear-gradient(135deg, var(--accent-red) 0%, #dc2626 100%);
}

.stat-gradient-yellow {
  background: linear-gradient(135deg, var(--accent-yellow) 0%, #ca8a04 100%);
}

.stat-gradient-orange {
  background: linear-gradient(135deg, var(--accent-orange) 0%, #d97706 100%);
}

/* Dark mode adjustments for better contrast */
.dark .stat-gradient-green {
  background: linear-gradient(135deg, var(--accent-green) 0%, #10b981 100%);
}

.dark .stat-gradient-red {
  background: linear-gradient(135deg, var(--accent-red) 0%, #ef4444 100%);
}

.dark .stat-gradient-yellow {
  background: linear-gradient(135deg, var(--accent-yellow) 0%, #eab308 100%);
}

.dark .stat-gradient-orange {
  background: linear-gradient(135deg, var(--accent-orange) 0%, #f59e0b 100%);
}

/* Theme-aware blue color utilities */
.theme-blue-50 { color: var(--accent-blue-50); }
.theme-blue-100 { color: var(--accent-blue-100); }
.theme-blue-200 { color: var(--accent-blue-200); }
.theme-blue-300 { color: var(--accent-blue-300); }
.theme-blue-400 { color: var(--accent-blue-400); }
.theme-blue-500 { color: var(--accent-blue-500); }
.theme-blue-600 { color: var(--accent-blue-600); }
.theme-blue-700 { color: var(--accent-blue-700); }
.theme-blue-800 { color: var(--accent-blue-800); }
.theme-blue-900 { color: var(--accent-blue-900); }

/* Mobile responsive utilities */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.touch-manipulation {
  touch-action: manipulation;
}

/* Fixed Settings Modal Styles */
/* Custom scrollbar styling for modal content */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Dark mode scrollbar */
.dark .overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

.dark .overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Mobile-specific responsive breakpoints */
@media (max-width: 320px) {
  /* Extra small mobile devices */
  .settings-modal-container {
    padding: 0.5rem;
  }

  .settings-content {
    padding: 0.75rem;
  }
}

@media (min-width: 321px) and (max-width: 480px) {
  /* Small mobile devices */
  .settings-modal-container {
    padding: 0.75rem;
  }

  .settings-content {
    padding: 1rem;
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  /* Large mobile devices and small tablets */
  .settings-modal-container {
    padding: 1rem;
  }

  .settings-content {
    padding: 1.25rem;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  /* Tablets */
  .settings-modal-container {
    padding: 1.5rem;
  }

  .settings-content {
    padding: 1.5rem;
  }
}

@media (min-width: 1025px) {
  /* Desktop */
  .settings-modal-container {
    padding: 2rem;
  }

  .settings-content {
    padding: 2rem;
  }
}

/* Ensure proper touch targets on mobile */
@media (max-width: 768px) {
  button, input, select, textarea, label {
    min-height: 44px;
  }

  /* Improve text readability on mobile */
  body {
    font-size: 16px; /* Prevents zoom on iOS */
  }

  /* Better spacing for mobile forms */
  .form-group {
    margin-bottom: 1.5rem;
  }

  /* Responsive text sizes */
  h1 { font-size: 1.5rem; }
  h2 { font-size: 1.25rem; }
  h3 { font-size: 1.125rem; }
  h4 { font-size: 1rem; }
}

.theme-bg-blue-50 { background-color: var(--accent-blue-50); }
.theme-bg-blue-100 { background-color: var(--accent-blue-100); }
.theme-bg-blue-200 { background-color: var(--accent-blue-200); }
.theme-bg-blue-300 { background-color: var(--accent-blue-300); }
.theme-bg-blue-400 { background-color: var(--accent-blue-400); }
.theme-bg-blue-500 { background-color: var(--accent-blue-500); }
.theme-bg-blue-600 { background-color: var(--accent-blue-600); }
.theme-bg-blue-700 { background-color: var(--accent-blue-700); }
.theme-bg-blue-800 { background-color: var(--accent-blue-800); }
.theme-bg-blue-900 { background-color: var(--accent-blue-900); }

.theme-border-blue-200 { border-color: var(--accent-blue-200); }
.theme-border-blue-300 { border-color: var(--accent-blue-300); }
.theme-border-blue-500 { border-color: var(--accent-blue-500); }
.theme-border-blue-600 { border-color: var(--accent-blue-600); }
.theme-border-blue-700 { border-color: var(--accent-blue-700); }
.theme-border-blue-800 { border-color: var(--accent-blue-800); }

/* Hover states */
.theme-hover-blue-600:hover { color: var(--accent-blue-600); }
.theme-hover-blue-700:hover { color: var(--accent-blue-700); }
.theme-hover-bg-blue-600:hover { background-color: var(--accent-blue-600); }
.theme-hover-bg-blue-700:hover { background-color: var(--accent-blue-700); }

/* Animation utilities for unified dashboard */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out forwards;
}
