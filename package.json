{"name": "qcode", "version": "1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:translations": "jest translations.test.ts", "export": "next build && next export", "deploy:vercel": "vercel", "deploy:vercel:prod": "vercel --prod", "clear-cache": "node cache-handler.js", "test:all": "jest && next lint && next build"}, "dependencies": {"@types/uuid": "^10.0.0", "date-fns": "^4.1.0", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "lucide-react": "^0.532.0", "next": "15.4.4", "next-i18next": "^15.4.2", "next-pwa": "^5.6.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-i18next": "^15.6.1", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@types/jest": "^30.0.0", "@types/minimatch": "^6.0.0", "@types/node": "^24.1.0", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.4", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "tailwindcss": "^4", "ts-jest": "^29.4.0", "typescript": "^5.8.3"}}